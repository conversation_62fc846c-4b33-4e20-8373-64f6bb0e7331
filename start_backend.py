#!/usr/bin/env python3
"""
启动后端服务器的脚本
"""
import os
import sys
import uvicorn

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

try:
    print("🚀 启动FastAPI服务器...")
    print("📍 地址: http://127.0.0.1:8001")
    print("📖 API文档: http://127.0.0.1:8001/docs")
    print("⏹️  按 Ctrl+C 停止服务器")

    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8001,
        log_level="info",
        access_log=True,
        reload=False
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)
