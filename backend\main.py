from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import pandas as pd
import numpy as np
from binance.client import Client
from binance import ThreadedWebsocketManager
import asyncio
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, List
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()
app = FastAPI(title="Crypto KLine API", version="1.0.0")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True
)

# Binance API配置
try:
    binance_client = Client(
        api_key=os.getenv("BINANCE_API_KEY", ""),
        api_secret=os.getenv("BINANCE_API_SECRET", ""),
        testnet=False  # 设置为True使用测试网
    )
    # 测试连接
    binance_client.ping()
    logger.info("Binance API连接成功")
except Exception as e:
    logger.warning(f"Binance API连接失败: {e}")
    # 创建一个模拟客户端用于开发测试
    binance_client = None

# 全局变量存储WebSocket连接
active_connections: Dict[str, List[WebSocket]] = {}

def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """计算技术指标"""
    try:
        # 计算移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        df['ma50'] = df['close'].rolling(window=50).mean()

        # 计算EMA
        df['ema12'] = df['close'].ewm(span=12).mean()
        df['ema26'] = df['close'].ewm(span=26).mean()

        # 计算MACD
        df['macd'] = df['ema12'] - df['ema26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']

        # 计算RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        return df
    except Exception as e:
        logger.error(f"计算技术指标失败: {e}")
        return df

def analyze_signals(df: pd.DataFrame) -> pd.DataFrame:
    """分析交易信号 - 改进版本"""
    try:
        df['trend'] = 'neutral'
        df['signal'] = None
        df['signal_strength'] = 0
        df['confidence'] = 0.0
        df['risk_level'] = 'medium'

        # 添加布林带
        df = calculate_bollinger_bands(df)

        # 添加成交量指标
        df = calculate_volume_indicators(df)

        for i in range(2, len(df)):  # 从第2个开始，需要更多历史数据
            current_idx = df.index[i]
            prev_idx = df.index[i-1]
            prev2_idx = df.index[i-2]

            # 基础均线信号
            ma_signals = analyze_ma_signals(df, i)

            # MACD信号
            macd_signals = analyze_macd_signals(df, i)

            # RSI信号
            rsi_signals = analyze_rsi_signals(df, i)

            # 布林带信号
            bb_signals = analyze_bollinger_signals(df, i)

            # 成交量确认
            volume_confirmation = analyze_volume_confirmation(df, i)

            # 价格行为分析
            price_action = analyze_price_action(df, i)

            # 综合信号评分
            total_score = (
                ma_signals['score'] * 0.3 +
                macd_signals['score'] * 0.25 +
                rsi_signals['score'] * 0.2 +
                bb_signals['score'] * 0.15 +
                volume_confirmation * 0.1
            )

            # 计算信心度
            confidence = min(abs(total_score) / 3.0, 1.0)

            # 风险评估
            risk_level = assess_risk_level(df, i, total_score)

            # 确定最终信号
            if total_score >= 2.5:
                signal = 'strong_buy'
                signal_strength = 3
                trend = 'bullish'
            elif total_score >= 1.5:
                signal = 'buy'
                signal_strength = 2
                trend = 'bullish'
            elif total_score >= 0.5:
                signal = 'weak_buy'
                signal_strength = 1
                trend = 'bullish'
            elif total_score <= -2.5:
                signal = 'strong_sell'
                signal_strength = -3
                trend = 'bearish'
            elif total_score <= -1.5:
                signal = 'sell'
                signal_strength = -2
                trend = 'bearish'
            elif total_score <= -0.5:
                signal = 'weak_sell'
                signal_strength = -1
                trend = 'bearish'
            else:
                signal = None
                signal_strength = 0
                trend = 'neutral'

            # 应用过滤器
            if apply_signal_filters(df, i, signal, confidence):
                df.loc[current_idx, 'signal'] = signal
                df.loc[current_idx, 'signal_strength'] = signal_strength
                df.loc[current_idx, 'trend'] = trend
                df.loc[current_idx, 'confidence'] = confidence
                df.loc[current_idx, 'risk_level'] = risk_level
            else:
                # 信号被过滤，保持趋势但清除信号
                df.loc[current_idx, 'signal'] = None
                df.loc[current_idx, 'signal_strength'] = 0
                df.loc[current_idx, 'trend'] = trend
                df.loc[current_idx, 'confidence'] = confidence * 0.5
                df.loc[current_idx, 'risk_level'] = risk_level

        return df
    except Exception as e:
        logger.error(f"分析交易信号失败: {e}")
        return df

def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> pd.DataFrame:
    """计算布林带"""
    try:
        df['bb_middle'] = df['close'].rolling(window=period).mean()
        bb_std = df['close'].rolling(window=period).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * std_dev)
        df['bb_lower'] = df['bb_middle'] - (bb_std * std_dev)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        return df
    except Exception as e:
        logger.error(f"计算布林带失败: {e}")
        return df

def calculate_volume_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """计算成交量指标"""
    try:
        # 成交量移动平均
        df['volume_ma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']

        # OBV (On Balance Volume)
        df['obv'] = 0.0
        for i in range(1, len(df)):
            if df['close'].iloc[i] > df['close'].iloc[i-1]:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1] + df['volume'].iloc[i]
            elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1] - df['volume'].iloc[i]
            else:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1]

        return df
    except Exception as e:
        logger.error(f"计算成交量指标失败: {e}")
        return df

def analyze_ma_signals(df: pd.DataFrame, i: int) -> dict:
    """分析均线信号"""
    try:
        current_idx = df.index[i]
        prev_idx = df.index[i-1]

        score = 0
        signals = []

        # MA5 vs MA10
        if df.loc[current_idx, 'ma5'] > df.loc[current_idx, 'ma10']:
            score += 0.5
            if df.loc[prev_idx, 'ma5'] <= df.loc[prev_idx, 'ma10']:
                score += 1.0  # 金叉
                signals.append('MA5金叉MA10')
        else:
            score -= 0.5
            if df.loc[prev_idx, 'ma5'] >= df.loc[prev_idx, 'ma10']:
                score -= 1.0  # 死叉
                signals.append('MA5死叉MA10')

        # MA10 vs MA20
        if df.loc[current_idx, 'ma10'] > df.loc[current_idx, 'ma20']:
            score += 0.5
            if df.loc[prev_idx, 'ma10'] <= df.loc[prev_idx, 'ma20']:
                score += 1.5  # 重要金叉
                signals.append('MA10金叉MA20')
        else:
            score -= 0.5
            if df.loc[prev_idx, 'ma10'] >= df.loc[prev_idx, 'ma20']:
                score -= 1.5  # 重要死叉
                signals.append('MA10死叉MA20')

        # 价格与均线关系
        close_price = df.loc[current_idx, 'close']
        if close_price > df.loc[current_idx, 'ma20']:
            score += 0.3
        else:
            score -= 0.3

        return {'score': score, 'signals': signals}
    except Exception as e:
        logger.error(f"分析均线信号失败: {e}")
        return {'score': 0, 'signals': []}

def analyze_macd_signals(df: pd.DataFrame, i: int) -> dict:
    """分析MACD信号"""
    try:
        current_idx = df.index[i]
        prev_idx = df.index[i-1]

        score = 0
        signals = []

        # MACD线与信号线交叉
        if df.loc[current_idx, 'macd'] > df.loc[current_idx, 'macd_signal']:
            score += 0.5
            if df.loc[prev_idx, 'macd'] <= df.loc[prev_idx, 'macd_signal']:
                score += 1.5  # MACD金叉
                signals.append('MACD金叉')
        else:
            score -= 0.5
            if df.loc[prev_idx, 'macd'] >= df.loc[prev_idx, 'macd_signal']:
                score -= 1.5  # MACD死叉
                signals.append('MACD死叉')

        # MACD柱状图
        if df.loc[current_idx, 'macd_histogram'] > 0:
            score += 0.3
        else:
            score -= 0.3

        # MACD背离检测（简化版）
        if i >= 10:
            recent_macd = df['macd'].iloc[i-10:i+1]
            recent_price = df['close'].iloc[i-10:i+1]

            if recent_price.iloc[-1] > recent_price.iloc[0] and recent_macd.iloc[-1] < recent_macd.iloc[0]:
                score -= 0.8  # 顶背离
                signals.append('MACD顶背离')
            elif recent_price.iloc[-1] < recent_price.iloc[0] and recent_macd.iloc[-1] > recent_macd.iloc[0]:
                score += 0.8  # 底背离
                signals.append('MACD底背离')

        return {'score': score, 'signals': signals}
    except Exception as e:
        logger.error(f"分析MACD信号失败: {e}")
        return {'score': 0, 'signals': []}

def analyze_rsi_signals(df: pd.DataFrame, i: int) -> dict:
    """分析RSI信号"""
    try:
        current_idx = df.index[i]
        prev_idx = df.index[i-1]

        score = 0
        signals = []

        rsi_current = df.loc[current_idx, 'rsi']
        rsi_prev = df.loc[prev_idx, 'rsi']

        # RSI超买超卖
        if rsi_current < 30:
            score += 1.0  # 超卖
            signals.append('RSI超卖')
            if rsi_prev >= 30:
                score += 0.5  # 刚进入超卖
        elif rsi_current > 70:
            score -= 1.0  # 超买
            signals.append('RSI超买')
            if rsi_prev <= 70:
                score -= 0.5  # 刚进入超买

        # RSI中性区间的趋势
        if 30 <= rsi_current <= 70:
            if rsi_current > rsi_prev:
                score += 0.2
            else:
                score -= 0.2

        # RSI 50线突破
        if rsi_current > 50 and rsi_prev <= 50:
            score += 0.5
            signals.append('RSI突破50')
        elif rsi_current < 50 and rsi_prev >= 50:
            score -= 0.5
            signals.append('RSI跌破50')

        return {'score': score, 'signals': signals}
    except Exception as e:
        logger.error(f"分析RSI信号失败: {e}")
        return {'score': 0, 'signals': []}

def analyze_bollinger_signals(df: pd.DataFrame, i: int) -> dict:
    """分析布林带信号"""
    try:
        current_idx = df.index[i]
        prev_idx = df.index[i-1]

        score = 0
        signals = []

        close_current = df.loc[current_idx, 'close']
        close_prev = df.loc[prev_idx, 'close']
        bb_upper = df.loc[current_idx, 'bb_upper']
        bb_lower = df.loc[current_idx, 'bb_lower']
        bb_position = df.loc[current_idx, 'bb_position']

        # 布林带位置
        if bb_position < 0.2:
            score += 0.8  # 接近下轨
            signals.append('接近布林下轨')
        elif bb_position > 0.8:
            score -= 0.8  # 接近上轨
            signals.append('接近布林上轨')

        # 突破布林带
        if close_current > bb_upper and close_prev <= bb_upper:
            score += 0.5  # 突破上轨
            signals.append('突破布林上轨')
        elif close_current < bb_lower and close_prev >= bb_lower:
            score -= 0.5  # 跌破下轨
            signals.append('跌破布林下轨')

        # 布林带宽度（波动性）
        bb_width = df.loc[current_idx, 'bb_width']
        if bb_width < 0.02:  # 窄幅震荡
            score *= 0.5  # 降低信号强度
            signals.append('布林带收窄')
        elif bb_width > 0.08:  # 高波动
            score *= 1.2  # 增强信号强度
            signals.append('布林带扩张')

        return {'score': score, 'signals': signals}
    except Exception as e:
        logger.error(f"分析布林带信号失败: {e}")
        return {'score': 0, 'signals': []}

def analyze_volume_confirmation(df: pd.DataFrame, i: int) -> float:
    """分析成交量确认"""
    try:
        current_idx = df.index[i]

        volume_ratio = df.loc[current_idx, 'volume_ratio']

        # 成交量放大确认信号
        if volume_ratio > 1.5:
            return 1.0  # 强确认
        elif volume_ratio > 1.2:
            return 0.5  # 中等确认
        elif volume_ratio < 0.8:
            return -0.3  # 成交量不足
        else:
            return 0.0  # 正常成交量

    except Exception as e:
        logger.error(f"分析成交量确认失败: {e}")
        return 0.0

def analyze_price_action(df: pd.DataFrame, i: int) -> dict:
    """分析价格行为"""
    try:
        current_idx = df.index[i]
        prev_idx = df.index[i-1]

        score = 0
        signals = []

        # K线形态分析
        open_price = df.loc[current_idx, 'open']
        high_price = df.loc[current_idx, 'high']
        low_price = df.loc[current_idx, 'low']
        close_price = df.loc[current_idx, 'close']

        body_size = abs(close_price - open_price)
        total_range = high_price - low_price

        if total_range > 0:
            body_ratio = body_size / total_range

            # 大阳线/大阴线
            if body_ratio > 0.7:
                if close_price > open_price:
                    score += 0.5
                    signals.append('大阳线')
                else:
                    score -= 0.5
                    signals.append('大阴线')

            # 十字星
            elif body_ratio < 0.1:
                signals.append('十字星')
                # 十字星在趋势中的意义需要结合位置判断

        # 价格突破
        if i >= 5:
            recent_high = df['high'].iloc[i-5:i].max()
            recent_low = df['low'].iloc[i-5:i].min()

            if close_price > recent_high:
                score += 0.8
                signals.append('突破近期高点')
            elif close_price < recent_low:
                score -= 0.8
                signals.append('跌破近期低点')

        return {'score': score, 'signals': signals}
    except Exception as e:
        logger.error(f"分析价格行为失败: {e}")
        return {'score': 0, 'signals': []}

def assess_risk_level(df: pd.DataFrame, i: int, signal_score: float) -> str:
    """评估风险等级"""
    try:
        current_idx = df.index[i]

        # 基于波动性评估风险
        if i >= 20:
            recent_volatility = df['close'].iloc[i-20:i+1].std() / df['close'].iloc[i-20:i+1].mean()

            if recent_volatility > 0.05:
                return 'high'
            elif recent_volatility < 0.02:
                return 'low'
            else:
                return 'medium'

        return 'medium'
    except Exception as e:
        logger.error(f"评估风险等级失败: {e}")
        return 'medium'

def apply_signal_filters(df: pd.DataFrame, i: int, signal: str, confidence: float) -> bool:
    """应用信号过滤器"""
    try:
        # 信心度过滤
        if confidence < 0.3:
            return False

        # 避免频繁信号
        if i >= 3:
            recent_signals = df['signal'].iloc[i-3:i].dropna()
            if len(recent_signals) >= 2:
                return False

        # 强信号总是通过
        if signal and ('strong' in signal):
            return True

        # 其他信号需要更高的信心度
        if confidence < 0.5:
            return False

        return True
    except Exception as e:
        logger.error(f"应用信号过滤器失败: {e}")
        return True

def process_kline_data(klines: List) -> pd.DataFrame:
    """处理K线数据"""
    try:
        columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume',
                  'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                  'taker_buy_quote', 'ignore']

        df = pd.DataFrame(klines, columns=columns)

        # 转换数据类型
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 选择需要的列
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]

        # 计算技术指标
        df = calculate_technical_indicators(df)

        # 分析交易信号
        df = analyze_signals(df)

        # 转换时间戳为字符串格式
        df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # 处理NaN值
        df = df.fillna(0)

        return df
    except Exception as e:
        logger.error(f"处理K线数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据处理失败: {str(e)}")

# API端点
@app.get("/")
async def root():
    return {"message": "Crypto KLine API", "status": "running"}

@app.get("/api/symbols")
async def get_symbols():
    """获取可用的交易对"""
    try:
        if binance_client is None:
            return {"symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT"]}  # 模拟数据

        exchange_info = binance_client.get_exchange_info()
        symbols = [s['symbol'] for s in exchange_info['symbols']
                  if s['status'] == 'TRADING' and s['symbol'].endswith('USDT')]
        return {"symbols": symbols[:50]}  # 返回前50个
    except Exception as e:
        logger.error(f"获取交易对失败: {e}")
        return {"symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT"]}

@app.get("/api/kline/{symbol}/{interval}")
async def get_kline(symbol: str, interval: str, limit: Optional[int] = 500, start_time: Optional[str] = None):
    """获取历史K线数据"""
    try:
        if binance_client is None:
            raise HTTPException(status_code=503, detail="Binance API未配置")

        # 参数验证
        valid_intervals = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
        if interval not in valid_intervals:
            raise HTTPException(status_code=400, detail=f"无效的时间间隔: {interval}")

        # 获取历史数据
        if start_time:
            klines = binance_client.get_historical_klines(symbol, interval, start_time, limit=limit)
        else:
            klines = binance_client.get_klines(symbol=symbol, interval=interval, limit=limit)

        if not klines:
            raise HTTPException(status_code=404, detail="未找到数据")

        # 处理数据
        df = process_kline_data(klines)

        return {
            "symbol": symbol,
            "interval": interval,
            "data": df.to_dict(orient='records')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# WebSocket连接管理
async def add_connection(symbol: str, interval: str, websocket: WebSocket):
    """添加WebSocket连接"""
    key = f"{symbol}_{interval}"
    if key not in active_connections:
        active_connections[key] = []
    active_connections[key].append(websocket)
    logger.info(f"新连接添加: {key}, 当前连接数: {len(active_connections[key])}")

async def remove_connection(symbol: str, interval: str, websocket: WebSocket):
    """移除WebSocket连接"""
    key = f"{symbol}_{interval}"
    if key in active_connections and websocket in active_connections[key]:
        active_connections[key].remove(websocket)
        if not active_connections[key]:
            del active_connections[key]
        logger.info(f"连接移除: {key}")

async def broadcast_to_connections(symbol: str, interval: str, data: dict):
    """向所有连接广播数据"""
    key = f"{symbol}_{interval}"
    if key not in active_connections:
        return

    disconnected = []
    for websocket in active_connections[key]:
        try:
            await websocket.send_json(data)
        except Exception as e:
            logger.warning(f"发送数据失败: {e}")
            disconnected.append(websocket)

    # 移除断开的连接
    for websocket in disconnected:
        await remove_connection(symbol, interval, websocket)

# 模拟实时数据生成器（用于测试）
async def generate_mock_kline_data(symbol: str, interval: str):
    """生成模拟K线数据"""
    import random
    base_price = 50000 if symbol == "BTCUSDT" else 3000

    while True:
        try:
            # 生成模拟数据
            timestamp = int(datetime.now().timestamp() * 1000)
            price_change = random.uniform(-0.02, 0.02)
            open_price = base_price * (1 + price_change)
            high_price = open_price * (1 + random.uniform(0, 0.01))
            low_price = open_price * (1 - random.uniform(0, 0.01))
            close_price = open_price * (1 + random.uniform(-0.01, 0.01))
            volume = random.uniform(100, 1000)

            kline_data = [
                timestamp, str(open_price), str(high_price), str(low_price),
                str(close_price), str(volume), timestamp + 60000, "0", "0", "0", "0", "0"
            ]

            df = process_kline_data([kline_data])

            data = {
                "symbol": symbol,
                "interval": interval,
                "data": df.to_dict(orient='records')[-1] if not df.empty else None
            }

            await broadcast_to_connections(symbol, interval, data)

            # 根据时间间隔调整更新频率
            sleep_time = 1 if interval in ['1m', '3m', '5m'] else 5
            await asyncio.sleep(sleep_time)

        except Exception as e:
            logger.error(f"生成模拟数据失败: {e}")
            await asyncio.sleep(5)

# 实时数据WebSocket
@app.websocket("/ws/kline/{symbol}/{interval}")
async def websocket_kline(websocket: WebSocket, symbol: str, interval: str):
    """WebSocket端点用于实时K线数据"""
    await websocket.accept()
    await add_connection(symbol, interval, websocket)

    try:
        if binance_client is None:
            # 如果没有Binance API，启动模拟数据生成器
            logger.info(f"启动模拟数据流: {symbol}_{interval}")
            task = asyncio.create_task(generate_mock_kline_data(symbol, interval))

            # 保持连接活跃
            while True:
                try:
                    # 等待客户端消息或连接断开
                    message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                    logger.info(f"收到客户端消息: {message}")
                except asyncio.TimeoutError:
                    # 发送心跳
                    await websocket.send_json({"type": "heartbeat", "timestamp": datetime.now().isoformat()})
                except WebSocketDisconnect:
                    break
        else:
            # 使用真实的Binance WebSocket
            from binance import ThreadedWebsocketManager

            def handle_socket_message(msg):
                """处理Binance WebSocket消息"""
                try:
                    if msg['e'] == 'kline':
                        kline = msg['k']
                        kline_data = [
                            kline['t'], kline['o'], kline['h'], kline['l'],
                            kline['c'], kline['v'], kline['T'], kline['q'],
                            kline['n'], kline['V'], kline['Q'], "0"
                        ]

                        df = process_kline_data([kline_data])

                        data = {
                            "symbol": symbol,
                            "interval": interval,
                            "data": df.to_dict(orient='records')[-1] if not df.empty else None,
                            "is_closed": kline['x']  # K线是否已关闭
                        }

                        # 异步广播数据
                        asyncio.create_task(broadcast_to_connections(symbol, interval, data))

                except Exception as e:
                    logger.error(f"处理WebSocket消息失败: {e}")

            # 启动Binance WebSocket
            twm = ThreadedWebsocketManager(
                api_key=os.getenv("BINANCE_API_KEY"),
                api_secret=os.getenv("BINANCE_API_SECRET")
            )
            twm.start()
            twm.start_kline_socket(callback=handle_socket_message, symbol=symbol, interval=interval)

            # 保持连接
            try:
                while True:
                    message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                    logger.info(f"收到客户端消息: {message}")
            except asyncio.TimeoutError:
                await websocket.send_json({"type": "heartbeat", "timestamp": datetime.now().isoformat()})
            except WebSocketDisconnect:
                pass
            finally:
                twm.stop()

    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {symbol}_{interval}")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
    finally:
        await remove_connection(symbol, interval, websocket)

# 启动服务器
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host=os.getenv("HOST", "localhost"),
        port=int(os.getenv("PORT", 8000)),
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )