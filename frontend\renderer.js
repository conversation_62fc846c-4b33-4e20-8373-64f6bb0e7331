// 全局变量
let dailyChart, hourlyChart, minuteChart;
let currentSymbol = 'BTCUSDT';
let wsConnections = {};
let chartData = {};

// 初始化图表
function initCharts() {
    try {
        // 初始化多周期K线图
        dailyChart = klinecharts.init('daily-chart');
        hourlyChart = klinecharts.init('hourly-chart');
        minuteChart = klinecharts.init('minute-chart');

        // 设置图表样式
        const chartStyle = {
            grid: {
                show: true,
                horizontal: { show: true, color: '#393939', style: 'dashed' },
                vertical: { show: true, color: '#393939', style: 'dashed' }
            },
            candle: {
                type: 'candle_solid',
                bar: {
                    upColor: '#26A69A',
                    downColor: '#EF5350',
                    noChangeColor: '#888888'
                },
                tooltip: { showRule: 'always', showType: 'standard' }
            },
            technicalIndicator: {
                margin: { top: 0.2, bottom: 0.1 },
                bar: { upColor: '#26A69A', downColor: '#EF5350', noChangeColor: '#888888' },
                line: { size: 1, colors: ['#FF9600', '#9D65C9', '#2196F3', '#E11D74', '#01C5C4'] },
                circle: { upColor: '#26A69A', downColor: '#EF5350', noChangeColor: '#888888' }
            }
        };

        dailyChart.setStyles(chartStyle);
        hourlyChart.setStyles(chartStyle);
        minuteChart.setStyles(chartStyle);

        console.log('图表初始化成功');
    } catch (error) {
        console.error('图表初始化失败:', error);
    }
}

// 处理K线数据格式
function formatKlineData(rawData) {
    if (!rawData || !Array.isArray(rawData)) {
        console.error('无效的K线数据:', rawData);
        return [];
    }

    return rawData.map(item => {
        const timestamp = new Date(item.timestamp).getTime();
        return {
            timestamp: timestamp,
            open: parseFloat(item.open),
            high: parseFloat(item.high),
            low: parseFloat(item.low),
            close: parseFloat(item.close),
            volume: parseFloat(item.volume),
            // 技术指标数据
            ma5: item.ma5 ? parseFloat(item.ma5) : null,
            ma10: item.ma10 ? parseFloat(item.ma10) : null,
            ma20: item.ma20 ? parseFloat(item.ma20) : null,
            ma50: item.ma50 ? parseFloat(item.ma50) : null,
            rsi: item.rsi ? parseFloat(item.rsi) : null,
            macd: item.macd ? parseFloat(item.macd) : null,
            // 交易信号
            trend: item.trend || 'neutral',
            signal: item.signal || null,
            signal_strength: item.signal_strength || 0
        };
    });
}

// 设置图表背景颜色根据趋势
function setChartBackground(chart, data) {
    try {
        if (!data || !Array.isArray(data)) return;

        // 创建背景注释
        const annotations = [];
        data.forEach((item, index) => {
            if (item.trend && item.trend !== 'neutral') {
                let backgroundColor;
                switch (item.trend) {
                    case 'bullish':
                        backgroundColor = 'rgba(38, 166, 154, 0.1)';
                        break;
                    case 'bearish':
                        backgroundColor = 'rgba(239, 83, 80, 0.1)';
                        break;
                    default:
                        backgroundColor = 'rgba(136, 136, 136, 0.05)';
                }

                annotations.push({
                    id: `trend_${index}`,
                    point: { timestamp: item.timestamp },
                    styles: { backgroundColor }
                });
            }

            // 添加交易信号标注
            if (item.signal && item.signal !== null) {
                const isLong = item.signal.includes('buy');
                annotations.push({
                    id: `signal_${index}`,
                    point: { timestamp: item.timestamp, value: isLong ? item.low * 0.995 : item.high * 1.005 },
                    styles: {
                        symbol: isLong ? 'triangleUp' : 'triangleDown',
                        color: isLong ? '#26A69A' : '#EF5350',
                        size: item.signal.includes('strong') ? 12 : 8
                    }
                });
            }
        });

        // 应用注释
        chart.createAnnotation(annotations);
    } catch (error) {
        console.error('设置图表背景失败:', error);
    }
}

// 添加技术指标
function addTechnicalIndicators(chart, chartType) {
    try {
        // 添加移动平均线
        chart.createTechnicalIndicator('MA', false, { id: 'MA', calcParams: [5, 10, 20, 50] });

        // 根据图表类型添加不同的指标
        if (chartType === 'daily') {
            chart.createTechnicalIndicator('MACD', true, { id: 'MACD' });
            chart.createTechnicalIndicator('RSI', true, { id: 'RSI' });
        } else if (chartType === 'hourly') {
            chart.createTechnicalIndicator('MACD', true, { id: 'MACD' });
        }

        // 设置指标交互
        chart.subscribeAction('onTechnicalIndicatorClick', (params) => {
            if (params && params.indicator) {
                const indicator = params.indicator;
                const message = `指标: ${indicator.name}\n参数: ${JSON.stringify(indicator.calcParams)}\n值: ${params.value || 'N/A'}`;
                showTooltip(message, params.x, params.y);
            }
        });

        console.log(`${chartType}图表技术指标添加成功`);
    } catch (error) {
        console.error('添加技术指标失败:', error);
    }
}

// 显示提示信息
function showTooltip(message, x, y) {
    // 创建或更新提示框
    let tooltip = document.getElementById('chart-tooltip');
    if (!tooltip) {
        tooltip = document.createElement('div');
        tooltip.id = 'chart-tooltip';
        tooltip.style.cssText = `
            position: fixed;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
            white-space: pre-line;
        `;
        document.body.appendChild(tooltip);
    }

    tooltip.textContent = message;
    tooltip.style.left = (x + 10) + 'px';
    tooltip.style.top = (y - 10) + 'px';
    tooltip.style.display = 'block';

    // 3秒后隐藏
    setTimeout(() => {
        tooltip.style.display = 'none';
    }, 3000);
}

// 加载历史数据
async function loadHistoricalData(symbol, interval, chart, chartType) {
    try {
        console.log(`加载历史数据: ${symbol} ${interval}`);

        const response = await fetch(`http://127.0.0.1:8001/api/kline/${symbol}/${interval}?limit=500`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.data || !Array.isArray(result.data)) {
            throw new Error('无效的数据格式');
        }

        const formattedData = formatKlineData(result.data);

        if (formattedData.length === 0) {
            throw new Error('没有可用数据');
        }

        // 存储数据
        chartData[`${symbol}_${interval}`] = formattedData;

        // 应用数据到图表
        chart.applyNewData(formattedData);

        // 设置背景颜色
        setChartBackground(chart, formattedData);

        // 添加技术指标
        addTechnicalIndicators(chart, chartType);

        console.log(`${chartType}图表数据加载成功，共${formattedData.length}条记录`);

        // 更新状态显示
        updateStatus(`${symbol} ${interval} 数据加载完成`);

    } catch (error) {
        console.error(`加载历史数据失败:`, error);
        updateStatus(`加载${symbol} ${interval}数据失败: ${error.message}`, 'error');
    }
}

// 设置WebSocket连接
function setupWebSocket(symbol, interval, chart, chartType) {
    const wsKey = `${symbol}_${interval}`;

    // 关闭现有连接
    if (wsConnections[wsKey]) {
        wsConnections[wsKey].close();
    }

    try {
        const ws = new WebSocket(`ws://127.0.0.1:8001/ws/kline/${symbol}/${interval}`);
        wsConnections[wsKey] = ws;

        ws.onopen = () => {
            console.log(`WebSocket连接已建立: ${wsKey}`);
            updateStatus(`${symbol} ${interval} 实时数据连接成功`);
            updateConnectionStatus(true);
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);

                if (data.type === 'heartbeat') {
                    console.log('收到心跳:', data.timestamp);
                    return;
                }

                if (data.data) {
                    const formattedData = formatKlineData([data.data]);
                    if (formattedData.length > 0) {
                        const newData = formattedData[0];

                        // 更新图表
                        chart.updateData(newData);

                        // 更新存储的数据
                        if (chartData[wsKey]) {
                            const lastIndex = chartData[wsKey].length - 1;
                            if (lastIndex >= 0) {
                                // 如果是同一时间戳，更新最后一条数据，否则添加新数据
                                if (chartData[wsKey][lastIndex].timestamp === newData.timestamp) {
                                    chartData[wsKey][lastIndex] = newData;
                                } else {
                                    chartData[wsKey].push(newData);
                                }
                            }
                        }

                        // 更新背景颜色和信号
                        if (newData.signal) {
                            setChartBackground(chart, [newData]);
                        }

                        console.log(`${chartType}图表实时数据更新:`, newData.close);
                    }
                }
            } catch (error) {
                console.error('处理WebSocket消息失败:', error);
            }
        };

        ws.onerror = (error) => {
            console.error(`WebSocket错误 ${wsKey}:`, error);
            updateStatus(`${symbol} ${interval} 连接错误`, 'error');
            updateConnectionStatus(false);
        };

        ws.onclose = (event) => {
            console.log(`WebSocket连接关闭 ${wsKey}:`, event.code, event.reason);
            updateStatus(`${symbol} ${interval} 连接断开`);
            updateConnectionStatus(false);

            // 5秒后尝试重连
            setTimeout(() => {
                if (!wsConnections[wsKey] || wsConnections[wsKey].readyState === WebSocket.CLOSED) {
                    console.log(`尝试重连 ${wsKey}`);
                    setupWebSocket(symbol, interval, chart, chartType);
                }
            }, 5000);
        };

    } catch (error) {
        console.error(`创建WebSocket连接失败 ${wsKey}:`, error);
        updateStatus(`创建${symbol} ${interval}连接失败`, 'error');
        updateConnectionStatus(false);
    }
}

// 更新状态显示
function updateStatus(message, type = 'info') {
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        statusElement.className = `status ${type}`;

        // 3秒后清除错误状态
        if (type === 'error') {
            setTimeout(() => {
                statusElement.className = 'status';
            }, 3000);
        }
    }
    console.log(`状态: ${message}`);
}

// 同步多周期图表
function syncCharts(timestamp) {
    try {
        if (!timestamp) {
            // 如果没有指定时间戳，使用当前时间
            timestamp = Date.now();
        }

        if (dailyChart && hourlyChart && minuteChart) {
            // 同步所有图表到指定时间戳
            dailyChart.scrollToTimestamp(timestamp);
            hourlyChart.scrollToTimestamp(timestamp);
            minuteChart.scrollToTimestamp(timestamp);

            updateStatus(`图表已同步到 ${new Date(timestamp).toLocaleString()}`);
        }
    } catch (error) {
        console.error('同步图表失败:', error);
        updateStatus('图表同步失败', 'error');
    }
}

// 高级同步功能：基于价格行为同步
function syncChartsByPriceAction() {
    try {
        // 获取当前显示的时间范围
        const dailyRange = dailyChart.getVisibleRange();
        if (!dailyRange) return;

        // 在其他图表中找到对应的时间范围
        const syncTimestamp = dailyRange.from + (dailyRange.to - dailyRange.from) / 2;

        // 同步到中心时间点
        syncCharts(syncTimestamp);

    } catch (error) {
        console.error('价格行为同步失败:', error);
    }
}

// 智能同步：根据重要事件同步
function smartSync() {
    try {
        // 查找最近的重要信号
        let latestSignalTime = null;
        let latestSignalStrength = 0;

        Object.entries(chartData).forEach(([, data]) => {
            if (data && Array.isArray(data)) {
                for (let i = data.length - 1; i >= Math.max(0, data.length - 50); i--) {
                    const item = data[i];
                    if (item.signal && Math.abs(item.signal_strength) > Math.abs(latestSignalStrength)) {
                        latestSignalTime = item.timestamp;
                        latestSignalStrength = item.signal_strength;
                    }
                }
            }
        });

        if (latestSignalTime) {
            syncCharts(new Date(latestSignalTime).getTime());
            updateStatus(`已同步到最强信号时间: ${new Date(latestSignalTime).toLocaleString()}`);
        } else {
            syncCharts();
        }

    } catch (error) {
        console.error('智能同步失败:', error);
        syncCharts(); // 回退到普通同步
    }
}

// 时间范围同步
function syncTimeRange(fromTimestamp, toTimestamp) {
    try {
        if (dailyChart && hourlyChart && minuteChart) {
            // 设置所有图表的可见范围
            dailyChart.setVisibleRange({ from: fromTimestamp, to: toTimestamp });
            hourlyChart.setVisibleRange({ from: fromTimestamp, to: toTimestamp });
            minuteChart.setVisibleRange({ from: fromTimestamp, to: toTimestamp });

            updateStatus(`时间范围已同步: ${new Date(fromTimestamp).toLocaleDateString()} - ${new Date(toTimestamp).toLocaleDateString()}`);
        }
    } catch (error) {
        console.error('时间范围同步失败:', error);
    }
}

// 缩放同步
function syncZoom(zoomLevel) {
    try {
        if (dailyChart && hourlyChart && minuteChart) {
            const currentTime = Date.now();
            const timeRange = zoomLevel * 24 * 60 * 60 * 1000; // 转换为毫秒

            const fromTime = currentTime - timeRange;
            const toTime = currentTime;

            syncTimeRange(fromTime, toTime);
        }
    } catch (error) {
        console.error('缩放同步失败:', error);
    }
}

// 切换交易对
async function changeSymbol(newSymbol) {
    if (newSymbol === currentSymbol) return;

    console.log(`切换交易对: ${currentSymbol} -> ${newSymbol}`);
    currentSymbol = newSymbol;

    // 关闭所有WebSocket连接
    Object.values(wsConnections).forEach(ws => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    });
    wsConnections = {};

    // 重新加载数据
    await initializeCharts();

    updateStatus(`已切换到 ${newSymbol}`);
}

// 初始化所有图表数据
async function initializeCharts() {
    try {
        updateStatus('正在加载图表数据...');

        // 并行加载历史数据
        const loadPromises = [
            loadHistoricalData(currentSymbol, '1d', dailyChart, 'daily'),
            loadHistoricalData(currentSymbol, '1h', hourlyChart, 'hourly'),
            loadHistoricalData(currentSymbol, '15m', minuteChart, 'minute')
        ];

        await Promise.all(loadPromises);

        // 设置WebSocket连接
        setupWebSocket(currentSymbol, '1d', dailyChart, 'daily');
        setupWebSocket(currentSymbol, '1h', hourlyChart, 'hourly');
        setupWebSocket(currentSymbol, '15m', minuteChart, 'minute');

        updateStatus('所有图表初始化完成');

    } catch (error) {
        console.error('初始化图表失败:', error);
        updateStatus('图表初始化失败', 'error');
    }
}

// 设置图表事件监听
function setupChartEvents() {
    const charts = [
        { chart: dailyChart, name: 'daily' },
        { chart: hourlyChart, name: 'hourly' },
        { chart: minuteChart, name: 'minute' }
    ];

    charts.forEach(({ chart, name }) => {
        if (!chart) return;

        // 缩放同步
        chart.subscribeAction('onZoom', (params) => {
            if (params && params.range) {
                // 同步其他图表的缩放
                charts.forEach(({ chart: otherChart, name: otherName }) => {
                    if (otherChart && otherName !== name) {
                        try {
                            otherChart.setVisibleRange(params.range);
                        } catch (error) {
                            console.warn(`同步${otherName}图表缩放失败:`, error);
                        }
                    }
                });

                updateStatus(`${name}图表缩放已同步`);
            }
        });

        // 十字光标同步
        chart.subscribeAction('onCrosshairChange', (params) => {
            if (params && params.timestamp) {
                // 同步其他图表的十字光标
                charts.forEach(({ chart: otherChart, name: otherName }) => {
                    if (otherChart && otherName !== name) {
                        try {
                            otherChart.setCrosshair({ timestamp: params.timestamp });
                        } catch (error) {
                            // 某些版本的KLineChart可能不支持setCrosshair
                            console.warn(`同步${otherName}图表十字光标失败:`, error);
                        }
                    }
                });

                // 更新指标显示（基于十字光标位置的数据）
                if (name === 'daily') {
                    updateIndicatorsAtTimestamp(params.timestamp);
                }
            }
        });

        // 点击事件
        chart.subscribeAction('onClick', (params) => {
            if (params && params.timestamp) {
                console.log(`${name}图表点击:`, new Date(params.timestamp).toLocaleString());
                syncCharts(params.timestamp);
            }
        });

        // 数据更新事件
        chart.subscribeAction('onDataReady', () => {
            console.log(`${name}图表数据就绪`);
        });
    });
}

// 根据时间戳更新指标显示
function updateIndicatorsAtTimestamp(timestamp) {
    try {
        const dailyData = chartData[`${currentSymbol}_1d`];
        if (!dailyData || !Array.isArray(dailyData)) return;

        // 找到最接近的数据点
        let closestData = null;
        let minDiff = Infinity;

        dailyData.forEach(item => {
            const itemTime = new Date(item.timestamp).getTime();
            const diff = Math.abs(itemTime - timestamp);
            if (diff < minDiff) {
                minDiff = diff;
                closestData = item;
            }
        });

        if (closestData) {
            updateIndicatorValues(closestData);
        }
    } catch (error) {
        console.error('更新时间戳指标失败:', error);
    }
}

// 添加快捷键支持
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
        // Ctrl+R: 刷新数据
        if (event.ctrlKey && event.key === 'r') {
            event.preventDefault();
            refreshData();
        }

        // Ctrl+S: 同步图表
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            smartSync();
        }

        // Ctrl+C: 清除图表
        if (event.ctrlKey && event.key === 'c') {
            event.preventDefault();
            clearCharts();
        }

        // 数字键1-3: 切换到不同时间范围
        if (event.key >= '1' && event.key <= '3') {
            const days = parseInt(event.key) * 7; // 1周、2周、3周
            syncZoom(days);
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('页面加载完成，开始初始化...');

        // 检查klinecharts是否加载
        if (typeof klinecharts === 'undefined') {
            throw new Error('KLineChart库未加载');
        }

        // 初始化图表
        initCharts();

        // 设置事件监听
        setupChartEvents();

        // 设置键盘快捷键
        setupKeyboardShortcuts();

        // 初始化连接状态
        updateConnectionStatus(false);

        // 等待一下再加载数据
        setTimeout(async () => {
            await initializeCharts();
        }, 500);

        console.log('应用初始化完成');

    } catch (error) {
        console.error('应用初始化失败:', error);
        updateStatus('应用初始化失败', 'error');
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    // 关闭所有WebSocket连接
    Object.values(wsConnections).forEach(ws => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.close();
        }
    });
    console.log('资源清理完成');
});

// UI控制函数
function refreshData() {
    updateStatus('正在刷新数据...');
    initializeCharts();
}

function clearCharts() {
    try {
        if (dailyChart) dailyChart.applyNewData([]);
        if (hourlyChart) hourlyChart.applyNewData([]);
        if (minuteChart) minuteChart.applyNewData([]);

        // 清除存储的数据
        chartData = {};

        updateStatus('图表已清除');
    } catch (error) {
        console.error('清除图表失败:', error);
        updateStatus('清除图表失败', 'error');
    }
}



function updateIndicatorValues(data) {
    if (!data) return;

    try {
        // 更新技术指标显示
        const indicators = {
            'ma5-value': data.ma5,
            'ma10-value': data.ma10,
            'ma20-value': data.ma20,
            'rsi-value': data.rsi,
            'macd-value': data.macd
        };

        Object.entries(indicators).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element && value !== null && value !== undefined) {
                element.textContent = typeof value === 'number' ? value.toFixed(2) : '--';
            }
        });

        // 更新交易信号
        const trendElement = document.getElementById('trend-signal');
        if (trendElement && data.trend) {
            trendElement.textContent = data.trend === 'bullish' ? '多头' :
                                     data.trend === 'bearish' ? '空头' : '中性';
            trendElement.className = `signal-indicator signal-${
                data.trend === 'bullish' ? 'buy' :
                data.trend === 'bearish' ? 'sell' : 'neutral'
            }`;
        }

        const signalElement = document.getElementById('trade-signal');
        if (signalElement && data.signal) {
            const signalText = data.signal.includes('buy') ? '买入' :
                              data.signal.includes('sell') ? '卖出' : '无';
            signalElement.textContent = signalText;
            signalElement.className = `signal-indicator signal-${
                data.signal.includes('buy') ? 'buy' :
                data.signal.includes('sell') ? 'sell' : 'neutral'
            }`;
        }

        const strengthElement = document.getElementById('signal-strength');
        if (strengthElement && data.signal_strength !== undefined) {
            strengthElement.textContent = data.signal_strength;
        }

    } catch (error) {
        console.error('更新指标值失败:', error);
    }
}

// 更新连接状态的辅助函数
function updateConnectionStatus(connected) {
    const dot = document.getElementById('connection-dot');
    const text = document.getElementById('connection-text');

    if (dot && text) {
        if (connected) {
            dot.classList.add('connected');
            text.textContent = '已连接';
        } else {
            dot.classList.remove('connected');
            text.textContent = '断开';
        }
    }
}

// 导出函数供HTML调用
window.changeSymbol = changeSymbol;
window.syncCharts = syncCharts;
window.smartSync = smartSync;
window.syncTimeRange = syncTimeRange;
window.syncZoom = syncZoom;
window.updateStatus = updateStatus;
window.refreshData = refreshData;
window.clearCharts = clearCharts;
window.updateIndicatorValues = updateIndicatorValues;