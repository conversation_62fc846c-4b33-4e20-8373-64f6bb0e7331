<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🚀 加密货币量化交易API测试</h1>
    
    <div class="container">
        <h2>📡 API连接测试</h2>
        <button class="btn" onclick="testConnection()">测试连接</button>
        <button class="btn" onclick="testSymbols()">获取交易对</button>
        <button class="btn" onclick="testKlineData()">获取K线数据</button>
        <div id="connectionResult" class="result"></div>
    </div>

    <div class="container">
        <h2>📊 WebSocket测试</h2>
        <button class="btn" onclick="connectWebSocket()">连接WebSocket</button>
        <button class="btn" onclick="disconnectWebSocket()">断开连接</button>
        <div id="wsStatus" class="status">未连接</div>
        <div id="wsResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001';
        const WS_BASE = 'ws://127.0.0.1:8001';
        let ws = null;

        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            element.textContent += logMessage;
            element.scrollTop = element.scrollHeight;
        }

        async function testConnection() {
            log('connectionResult', '🔍 测试API连接...');
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                log('connectionResult', `✅ 连接成功: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log('connectionResult', `❌ 连接失败: ${error.message}`, true);
            }
        }

        async function testSymbols() {
            log('connectionResult', '🔍 获取交易对列表...');
            try {
                const response = await fetch(`${API_BASE}/api/symbols`);
                const data = await response.json();
                log('connectionResult', `✅ 获取成功，共${data.symbols.length}个交易对`);
                log('connectionResult', `前10个: ${data.symbols.slice(0, 10).join(', ')}`);
            } catch (error) {
                log('connectionResult', `❌ 获取失败: ${error.message}`, true);
            }
        }

        async function testKlineData() {
            log('connectionResult', '🔍 获取K线数据...');
            try {
                const response = await fetch(`${API_BASE}/api/kline/BTCUSDT/1h?limit=10`);
                const data = await response.json();
                log('connectionResult', `✅ 获取成功，共${data.data.length}条数据`);
                if (data.data.length > 0) {
                    const latest = data.data[data.data.length - 1];
                    log('connectionResult', `最新数据: 时间=${latest.timestamp}, 收盘价=${latest.close}`);
                    if (latest.signal) {
                        log('connectionResult', `交易信号: ${latest.signal} (强度: ${latest.signal_strength})`);
                    }
                }
            } catch (error) {
                log('connectionResult', `❌ 获取失败: ${error.message}`, true);
            }
        }

        function connectWebSocket() {
            if (ws) {
                ws.close();
            }

            log('wsResult', '🔗 连接WebSocket...');
            const statusElement = document.getElementById('wsStatus');
            
            try {
                ws = new WebSocket(`${WS_BASE}/ws/kline/BTCUSDT/1h`);
                
                ws.onopen = () => {
                    log('wsResult', '✅ WebSocket连接成功');
                    statusElement.textContent = '已连接';
                    statusElement.className = 'status success';
                };

                ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'heartbeat') {
                            log('wsResult', `💓 心跳: ${data.timestamp}`);
                        } else if (data.data) {
                            log('wsResult', `📊 实时数据: 价格=${data.data.close}, 时间=${data.data.timestamp}`);
                            if (data.data.signal) {
                                log('wsResult', `🚨 交易信号: ${data.data.signal}`);
                            }
                        }
                    } catch (error) {
                        log('wsResult', `❌ 解析消息失败: ${error.message}`, true);
                    }
                };

                ws.onerror = (error) => {
                    log('wsResult', `❌ WebSocket错误: ${error}`, true);
                    statusElement.textContent = '连接错误';
                    statusElement.className = 'status error';
                };

                ws.onclose = (event) => {
                    log('wsResult', `🔌 WebSocket连接关闭: ${event.code} ${event.reason}`);
                    statusElement.textContent = '已断开';
                    statusElement.className = 'status';
                };

            } catch (error) {
                log('wsResult', `❌ 创建WebSocket失败: ${error.message}`, true);
                statusElement.textContent = '连接失败';
                statusElement.className = 'status error';
            }
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('wsResult', '🔌 手动断开WebSocket连接');
            }
        }

        // 页面加载时自动测试连接
        window.addEventListener('load', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
