#!/usr/bin/env python3
"""
修复脚本 - 自动修复项目中的已知问题
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_step(step_num, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print('='*60)

def run_command(command, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True,
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def check_python_version():
    """检查Python版本"""
    print_step(1, "检查Python版本")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 警告: 建议使用Python 3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def upgrade_dependencies():
    """升级依赖包"""
    print_step(2, "升级项目依赖")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 升级python-binance
    print("升级python-binance到最新版本...")
    success, output = run_command("pip install python-binance==1.0.28", cwd=backend_dir)
    
    if success:
        print("✅ python-binance升级成功")
    else:
        print(f"❌ python-binance升级失败: {output}")
        return False
    
    # 升级其他依赖
    print("升级其他依赖包...")
    success, output = run_command("pip install -r requirements.txt --upgrade", cwd=backend_dir)
    
    if success:
        print("✅ 依赖包升级完成")
        return True
    else:
        print(f"❌ 依赖包升级失败: {output}")
        return False

def update_env_config():
    """更新环境配置"""
    print_step(3, "更新环境配置")
    
    env_file = Path("backend/.env")
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    
    # 读取现有配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加新的配置项
    new_configs = [
        "\n# 连接配置优化",
        "WEBSOCKET_TIMEOUT=30",
        "RECONNECT_INTERVAL=5",
        "MAX_RECONNECT_ATTEMPTS=3",
        "",
        "# 性能优化",
        "DATA_COMPRESSION=true",
        "BATCH_SIZE=1000",
        "",
        "# 监控配置", 
        "ENABLE_METRICS=true",
        "LOG_PERFORMANCE=true"
    ]
    
    # 检查是否已存在这些配置
    if "WEBSOCKET_TIMEOUT" not in content:
        content += "\n" + "\n".join(new_configs)
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 环境配置已更新")
    else:
        print("✅ 环境配置已是最新")
    
    return True

def create_backup():
    """创建备份"""
    print_step(4, "创建项目备份")
    
    backup_dir = Path("backup")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    
    # 创建备份目录
    backup_dir.mkdir()
    
    # 备份关键文件
    files_to_backup = [
        "backend/main.py",
        "frontend/renderer.js", 
        "frontend/index.html",
        "backend/requirements.txt",
        "backend/.env"
    ]
    
    for file_path in files_to_backup:
        src = Path(file_path)
        if src.exists():
            dst = backup_dir / src.name
            shutil.copy2(src, dst)
            print(f"✅ 已备份: {file_path}")
    
    print("✅ 备份完成")
    return True

def verify_fixes():
    """验证修复结果"""
    print_step(5, "验证修复结果")
    
    checks = []
    
    # 检查WebSocket连接代码
    renderer_file = Path("frontend/renderer.js")
    if renderer_file.exists():
        with open(renderer_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含正确的WebSocket地址
        if "ws://127.0.0.1:8001" in content:
            checks.append(("WebSocket地址", True))
        else:
            checks.append(("WebSocket地址", False))
        
        # 检查是否包含连接状态更新
        if "updateConnectionStatus" in content:
            checks.append(("连接状态更新", True))
        else:
            checks.append(("连接状态更新", False))
    
    # 检查依赖版本
    try:
        import binance
        version = binance.__version__
        if version >= "1.0.28":
            checks.append(("python-binance版本", True))
        else:
            checks.append(("python-binance版本", False))
    except ImportError:
        checks.append(("python-binance版本", False))
    
    # 检查环境配置
    env_file = Path("backend/.env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        if "WEBSOCKET_TIMEOUT" in env_content:
            checks.append(("环境配置", True))
        else:
            checks.append(("环境配置", False))
    
    # 输出检查结果
    print("\n验证结果:")
    all_passed = True
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}: {'通过' if passed else '失败'}")
        if not passed:
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("🚀 开始修复加密货币量化交易K线图项目")
    print("本脚本将自动修复已知问题并优化配置")
    
    # 检查当前目录
    if not Path("backend").exists() or not Path("frontend").exists():
        print("❌ 错误: 请在项目根目录下运行此脚本")
        sys.exit(1)
    
    steps = [
        check_python_version,
        create_backup,
        upgrade_dependencies, 
        update_env_config,
        verify_fixes
    ]
    
    failed_steps = []
    
    for i, step_func in enumerate(steps, 1):
        try:
            success = step_func()
            if not success:
                failed_steps.append(step_func.__name__)
        except Exception as e:
            print(f"❌ 步骤 {i} 执行失败: {e}")
            failed_steps.append(step_func.__name__)
    
    # 输出总结
    print("\n" + "="*60)
    print("修复完成总结")
    print("="*60)
    
    if not failed_steps:
        print("🎉 所有修复步骤都已成功完成!")
        print("\n下一步操作:")
        print("1. 重启后端服务: cd backend && python main.py")
        print("2. 重启前端应用: cd frontend && npm start")
        print("3. 检查连接状态是否显示'已连接'")
    else:
        print(f"⚠️  以下步骤执行失败: {', '.join(failed_steps)}")
        print("请查看上面的错误信息并手动修复")
    
    print("\n📖 详细的优化建议请查看: 优化建议和修复方案.md")

if __name__ == "__main__":
    main()
