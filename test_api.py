#!/usr/bin/env python3
"""
测试API功能
"""
import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

def test_import():
    """测试模块导入"""
    try:
        print("🔍 测试模块导入...")
        from main import app
        print("✅ 后端模块导入成功")
        return app
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return None

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("🔍 测试基本功能...")
        
        # 测试数据处理函数
        from main import calculate_technical_indicators, process_kline_data
        import pandas as pd
        
        # 创建测试数据
        test_data = [
            [1640995200000, "50000", "51000", "49000", "50500", "100", 1640995260000, "5050000", 1000, "50", "2525000", "0"]
        ]
        
        df = process_kline_data(test_data)
        print(f"✅ 数据处理成功，生成 {len(df)} 条记录")
        
        if not df.empty:
            print(f"📊 示例数据: 开盘价={df.iloc[0]['open']}, 收盘价={df.iloc[0]['close']}")
            if 'ma10' in df.columns:
                print(f"📈 技术指标: MA10={df.iloc[0]['ma10']}")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    try:
        print("🔍 测试API端点...")
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        
        # 测试根端点
        response = client.get("/")
        print(f"GET / - Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        
        # 测试symbols端点
        response = client.get("/api/symbols")
        print(f"GET /api/symbols - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"可用交易对数量: {len(data.get('symbols', []))}")
        
        print("✅ API端点测试完成")
        return True
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API功能测试")
    print("=" * 50)
    
    # 测试模块导入
    app = test_import()
    if not app:
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        return
    
    # 测试API端点
    if not test_api_endpoints():
        return
    
    print("=" * 50)
    print("🎉 所有测试通过！")
    print("💡 提示：现在可以启动前端应用进行完整测试")

if __name__ == "__main__":
    main()
